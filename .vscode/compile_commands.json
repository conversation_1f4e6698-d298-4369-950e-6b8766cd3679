[{"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/pianowo", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include", "-isystem", "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include", "-isystem", "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include", "-DNDEBUG", "-o", "build/.objs/pianowo/linux/x86_64/release/src/alsa_midi.cpp.o", "src/alsa_midi.cpp"], "file": "src/alsa_midi.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/pianowo", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include", "-isystem", "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include", "-isystem", "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include", "-DNDEBUG", "-o", "build/.objs/pianowo/linux/x86_64/release/src/audio_engine.cpp.o", "src/audio_engine.cpp"], "file": "src/audio_engine.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/pianowo", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include", "-isystem", "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include", "-isystem", "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include", "-DNDEBUG", "-o", "build/.objs/pianowo/linux/x86_64/release/src/audio_limiter.cpp.o", "src/audio_limiter.cpp"], "file": "src/audio_limiter.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/pianowo", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include", "-isystem", "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include", "-isystem", "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include", "-DNDEBUG", "-o", "build/.objs/pianowo/linux/x86_64/release/src/bass_multisynth.cpp.o", "src/bass_multisynth.cpp"], "file": "src/bass_multisynth.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/pianowo", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include", "-isystem", "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include", "-isystem", "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include", "-DNDEBUG", "-o", "build/.objs/pianowo/linux/x86_64/release/src/config_manager.cpp.o", "src/config_manager.cpp"], "file": "src/config_manager.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/pianowo", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include", "-isystem", "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include", "-isystem", "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include", "-DNDEBUG", "-o", "build/.objs/pianowo/linux/x86_64/release/src/main.cpp.o", "src/main.cpp"], "file": "src/main.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/pianowo", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include", "-isystem", "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include", "-isystem", "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include", "-DNDEBUG", "-o", "build/.objs/pianowo/linux/x86_64/release/src/multithreaded_midi_engine.cpp.o", "src/multithreaded_midi_engine.cpp"], "file": "src/multithreaded_midi_engine.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/pianowo", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include", "-isystem", "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include", "-isystem", "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include", "-DNDEBUG", "-o", "build/.objs/pianowo/linux/x86_64/release/src/opengl_renderer.cpp.o", "src/opengl_renderer.cpp"], "file": "src/opengl_renderer.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/pianowo", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include", "-isystem", "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include", "-isystem", "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include", "-DNDEBUG", "-o", "build/.objs/pianowo/linux/x86_64/release/src/piano_keyboard.cpp.o", "src/piano_keyboard.cpp"], "file": "src/piano_keyboard.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/pianowo", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends", "-isystem", "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxrender/0.9.12/75df963310db406385383085b95b222e/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxfixes/6.0.1/31f0eaa885a84ef6b740f6ed04fea3e3/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/home/<USER>/.xmake/packages/l/libx11/1.8.12/5b3492e889e848b2b511e3f3031c5538/include", "-isystem", "/home/<USER>/.xmake/packages/x/xtrans/1.6.0/588d85a9c99148b8ae87dee11b39ab40/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxcb/1.17.0/27d2233a10b34ff5971001208db29e2c/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxau/1.0.12/f406943012fc443596a2e2e23215839d/include", "-isystem", "/home/<USER>/.xmake/packages/l/libxdmcp/1.1.5/1e5cac3a0ef94e939a1a5bb7efb8c24f/include", "-isystem", "/home/<USER>/.xmake/packages/x/xorgproto/2023.2/3a1682704c034fbf87583b32455dde94/include", "-DNDEBUG", "-o", "build/.objs/pianowo/linux/x86_64/release/src/winmm_midi.cpp.o", "src/winmm_midi.cpp"], "file": "src/winmm_midi.cpp"}]