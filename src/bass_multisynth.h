#pragma once

#include <vector>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <atomic>
#include <thread>
#include <chrono>
#include <string>

#if defined(_WIN32)
#include <windows.h>
#else
#include <dlfcn.h>
#endif

#define NOBASSMIDIOVERLOADS
#include "bass.h"
#include "bassmidi.h"

// Forward declaration
class PianoKeyboard;

// Note key structure for mapping notes to synth instances
struct NoteKey {
    uint8_t channel;
    uint8_t note;
    
    bool operator==(const NoteKey& other) const {
        return channel == other.channel && note == other.note;
    }
};

// Hash function for NoteKey
struct NoteKeyHash {
    std::size_t operator()(const NoteKey& key) const {
        return std::hash<uint16_t>()((static_cast<uint16_t>(key.channel) << 8) | key.note);
    }
};

// BASS MIDI synthesizer instance
class BASSMIDISynth {
public:
    BASSMIDISynth();
    ~BASSMIDISynth();
    
    bool Initialize(int instance_id, uint32_t max_voices, HSOUNDFONT soundfont);
    void Cleanup();
    
    bool QueueMIDICommand(uint32_t cmd);
    void ProcessMIDIQueue();
    
    uint32_t GetCurrentVoices() const { return current_voices_.load(); }
    uint32_t GetMaxVoices() const { return max_voices_; }
    
    void SetVolume(float volume);
    float GetVolume() const { return volume_; }
    
    bool IsInitialized() const { return initialized_; }
    int GetInstanceId() const { return instance_id_; }
    
    // Voice management
    void IncrementVoices() { current_voices_.fetch_add(1); }
    void DecrementVoices() {
        uint32_t current = current_voices_.load();
        if (current > 0) {
            current_voices_.fetch_sub(1);
        }
    }
    void ResetVoices() { current_voices_.store(0); }
    
    // Performance metrics
    std::chrono::duration<double> GetLastRenderTime() const { return last_render_time_; }
    void SetLastRenderTime(std::chrono::duration<double> time) { last_render_time_ = time; }

    // Get MIDI stream handle for BASS queries
    HSTREAM GetMIDIStream() const { return midi_stream_; }

private:
    HSTREAM midi_stream_;
    int instance_id_;
    uint32_t max_voices_;
    std::atomic<uint32_t> current_voices_;
    float volume_;
    bool initialized_;
    
    // MIDI command queue
    std::mutex queue_mutex_;
    std::vector<uint32_t> midi_queue_;
    
    // Performance tracking
    std::chrono::duration<double> last_render_time_;
    
    // BASS function pointers (shared)
    static decltype(&BASS_MIDI_StreamCreate) BASS_MIDI_StreamCreate_ptr;
    static decltype(&BASS_MIDI_StreamEvent) BASS_MIDI_StreamEvent_ptr;
    static decltype(&BASS_ChannelPlay) BASS_ChannelPlay_ptr;
    static decltype(&BASS_ChannelSetAttribute) BASS_ChannelSetAttribute_ptr;
    static decltype(&BASS_StreamFree) BASS_StreamFree_ptr;
    static decltype(&BASS_MIDI_StreamSetFonts) BASS_MIDI_StreamSetFonts_ptr;
    
public:
    // Static method to set function pointers
    static void SetBASSFunctionPointers(
        decltype(&BASS_MIDI_StreamCreate) create_ptr,
        decltype(&BASS_MIDI_StreamEvent) event_ptr,
        decltype(&BASS_ChannelPlay) play_ptr,
        decltype(&BASS_ChannelSetAttribute) attr_ptr,
        decltype(&BASS_StreamFree) free_ptr,
        decltype(&BASS_MIDI_StreamSetFonts) set_fonts_ptr
    );
};

// Multi-synthesizer engine inspired by Rust MultiSynth
class BASSMultiSynth {
public:
    BASSMultiSynth();
    ~BASSMultiSynth();
    
    bool Initialize(
        uint32_t max_total_voices,
        uint32_t num_instances,
        HSOUNDFONT soundfont = 0
    );
    void Cleanup();
    
    // MIDI command processing (similar to Rust queue_midi_cmd)
    void QueueMIDICommand(uint32_t cmd);
    
    // Note management
    void NoteOn(uint8_t channel, uint8_t note, uint8_t velocity);
    void NoteOff(uint8_t channel, uint8_t note, uint8_t velocity);
    
    // Control changes and other MIDI events
    void SendControlChange(uint8_t channel, uint8_t controller, uint8_t value);
    void SendProgramChange(uint8_t channel, uint8_t program);
    void StopAllNotes();
    
    // Audio processing (similar to Rust fill_buffer)
    void ProcessAudio();
    
    // Performance metrics (similar to Rust methods)
    uint32_t GetPolyphony() const;
    uint32_t GetMaxPolyphony() const;
    float GetRenderingTimeRatio() const;
    
    // Configuration
    void SetMasterVolume(float volume);
    float GetMasterVolume() const { return master_volume_; }
    
    bool LoadSoundfont(const std::string& soundfont_path);
    
    // Piano keyboard integration
    void SetPianoKeyboard(PianoKeyboard* piano_keyboard);
    
    bool IsInitialized() const { return initialized_; }
    
    // Performance statistics
    struct PerformanceStats {
        uint32_t total_voices;
        uint32_t max_voices;
        float avg_rendering_time_ms;
        std::vector<uint32_t> instance_voices;
        std::vector<float> instance_loads;
    };
    
    PerformanceStats GetPerformanceStats() const;

private:
    bool initialized_;
    std::vector<std::unique_ptr<BASSMIDISynth>> synths_;
    std::vector<uint32_t> max_voices_per_instance_;
    
    // Note mapping (similar to Rust note_map)
    std::unordered_map<NoteKey, std::vector<size_t>, NoteKeyHash> note_map_;
    mutable std::mutex note_map_mutex_;
    
    // Voice counting (similar to Rust note_counts)
    std::vector<std::unique_ptr<std::atomic<uint32_t>>> note_counts_;
    
    HSOUNDFONT soundfont_;
    std::string current_soundfont_path_;
    std::atomic<float> master_volume_;
    
    // Piano keyboard for visual feedback
    PianoKeyboard* piano_keyboard_;
    
    // Performance tracking
    mutable std::mutex stats_mutex_;
    std::chrono::high_resolution_clock::time_point last_stats_update_;
    
    // Helper methods
    size_t SelectBestInstance() const;
    void NotifyMIDIKeyPressed(uint8_t note, bool pressed);
    void UpdatePerformanceStats();
    
    // BASS function pointers
    decltype(&BASS_MIDI_StreamCreate) BASS_MIDI_StreamCreate_ptr_;
    decltype(&BASS_MIDI_StreamEvent) BASS_MIDI_StreamEvent_ptr_;
    decltype(&BASS_ChannelPlay) BASS_ChannelPlay_ptr_;
    decltype(&BASS_ChannelSetAttribute) BASS_ChannelSetAttribute_ptr_;
    decltype(&BASS_ChannelGetAttribute) BASS_ChannelGetAttribute_ptr_;
    decltype(&BASS_StreamFree) BASS_StreamFree_ptr_;
    decltype(&BASS_MIDI_FontInit) BASS_MIDI_FontInit_ptr_;
    decltype(&BASS_MIDI_StreamSetFonts) BASS_MIDI_StreamSetFonts_ptr_;
    
public:
    // Method to set BASS function pointers
    void SetBASSFunctionPointers(
        decltype(&BASS_MIDI_StreamCreate) create_ptr,
        decltype(&BASS_MIDI_StreamEvent) event_ptr,
        decltype(&BASS_ChannelPlay) play_ptr,
        decltype(&BASS_ChannelSetAttribute) attr_ptr,
        decltype(&BASS_ChannelGetAttribute) get_attr_ptr,
        decltype(&BASS_StreamFree) free_ptr,
        decltype(&BASS_MIDI_FontInit) font_init_ptr,
        decltype(&BASS_MIDI_StreamSetFonts) set_fonts_ptr
    );
};
