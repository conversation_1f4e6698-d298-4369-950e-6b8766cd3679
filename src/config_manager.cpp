#include "config_manager.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <filesystem>

#ifdef _WIN32
#include <windows.h>
#include <shlobj.h>
#else
#include <unistd.h>
#include <pwd.h>
#include <sys/stat.h>
#endif

// Simple JSON-like parser and serializer
// Note: This is a minimal implementation. For production, consider using a proper JSON library
namespace SimpleJSON {
    std::string EscapeString(const std::string& str) {
        std::string escaped;
        for (char c : str) {
            switch (c) {
                case '"': escaped += "\\\""; break;
                case '\\': escaped += "\\\\"; break;
                case '\n': escaped += "\\n"; break;
                case '\r': escaped += "\\r"; break;
                case '\t': escaped += "\\t"; break;
                default: escaped += c; break;
            }
        }
        return escaped;
    }

    std::string UnescapeString(const std::string& str) {
        std::string unescaped;
        for (size_t i = 0; i < str.length(); ++i) {
            if (str[i] == '\\' && i + 1 < str.length()) {
                switch (str[i + 1]) {
                    case '"': unescaped += '"'; i++; break;
                    case '\\': unescaped += '\\'; i++; break;
                    case 'n': unescaped += '\n'; i++; break;
                    case 'r': unescaped += '\r'; i++; break;
                    case 't': unescaped += '\t'; i++; break;
                    default: unescaped += str[i]; break;
                }
            } else {
                unescaped += str[i];
            }
        }
        return unescaped;
    }

    // Extract value from JSON string (simple implementation)
    std::string GetStringValue(const std::string& json, const std::string& key) {
        std::string search = "\"" + key + "\"";
        size_t pos = json.find(search);
        if (pos == std::string::npos) return "";
        
        pos = json.find(":", pos);
        if (pos == std::string::npos) return "";
        
        pos = json.find("\"", pos);
        if (pos == std::string::npos) return "";
        pos++; // Skip opening quote
        
        size_t end = json.find("\"", pos);
        if (end == std::string::npos) return "";
        
        return UnescapeString(json.substr(pos, end - pos));
    }

    float GetFloatValue(const std::string& json, const std::string& key) {
        std::string search = "\"" + key + "\"";
        size_t pos = json.find(search);
        if (pos == std::string::npos) return 0.0f;
        
        pos = json.find(":", pos);
        if (pos == std::string::npos) return 0.0f;
        
        // Skip whitespace
        while (pos < json.length() && (json[pos] == ':' || json[pos] == ' ' || json[pos] == '\t')) pos++;
        
        size_t end = pos;
        while (end < json.length() && (std::isdigit(json[end]) || json[end] == '.' || json[end] == '-')) end++;
        
        if (end > pos) {
            return std::stof(json.substr(pos, end - pos));
        }
        return 0.0f;
    }

    int GetIntValue(const std::string& json, const std::string& key) {
        return static_cast<int>(GetFloatValue(json, key));
    }

    bool GetBoolValue(const std::string& json, const std::string& key) {
        std::string search = "\"" + key + "\"";
        size_t pos = json.find(search);
        if (pos == std::string::npos) return false;
        
        pos = json.find(":", pos);
        if (pos == std::string::npos) return false;
        
        return json.find("true", pos) < json.find("false", pos);
    }
}

ConfigManager::ConfigManager() 
    : auto_save_enabled_(true), config_dirty_(false) {
}

ConfigManager::~ConfigManager() {
    if (auto_save_enabled_ && config_dirty_) {
        SaveConfig();
    }
}

bool ConfigManager::Initialize(const std::string& config_file_path) {
    if (config_file_path.empty()) {
        config_file_path_ = GetDefaultConfigPath();
    } else {
        config_file_path_ = config_file_path;
    }

    std::cout << "Config file path: " << config_file_path_ << std::endl;

    // Create config directory if it doesn't exist
    std::string config_dir = std::filesystem::path(config_file_path_).parent_path().string();
    if (!CreateDirectoryIfNotExists(config_dir)) {
        std::cerr << "Failed to create config directory: " << config_dir << std::endl;
        return false;
    }

    // Load existing config or create default
    if (!LoadConfig()) {
        std::cout << "No existing config found, using defaults" << std::endl;
        ResetToDefaults();
        SaveConfig(); // Save default config
    }

    return true;
}

bool ConfigManager::LoadConfig() {
    std::string content;
    if (!ReadFile(config_file_path_, content)) {
        return false;
    }

    return LoadFromJSON(content);
}

bool ConfigManager::SaveConfig() {
    std::string json_content = SaveToJSON();
    if (WriteFile(config_file_path_, json_content)) {
        config_dirty_ = false;
        return true;
    }
    return false;
}

void ConfigManager::AutoSave() {
    if (auto_save_enabled_) {
        MarkDirty();
        SaveConfig();
    }
}

void ConfigManager::ResetToDefaults() {
    config_ = AppConfig(); // Reset to default values
    config_dirty_ = true;
}

std::string ConfigManager::GetDefaultConfigPath() {
    std::string config_dir = GetConfigDirectory();
    return config_dir + "/pianowo_config.json";
}

std::string ConfigManager::GetConfigDirectory() {
#ifdef _WIN32
    char path[MAX_PATH];
    if (SUCCEEDED(SHGetFolderPathA(NULL, CSIDL_APPDATA, NULL, 0, path))) {
        return std::string(path) + "/pianowo";
    }
    return "./config";
#else
    const char* home = getenv("HOME");
    if (!home) {
        struct passwd* pw = getpwuid(getuid());
        if (pw) home = pw->pw_dir;
    }
    if (home) {
        return std::string(home) + "/.config/pianowo";
    }
    return "./config";
#endif
}

std::string ConfigManager::GetExecutableDirectory() {
#ifdef _WIN32
    char path[MAX_PATH];
    GetModuleFileNameA(NULL, path, MAX_PATH);
    std::string exe_path(path);
    return exe_path.substr(0, exe_path.find_last_of("\\/"));
#else
    char path[1024];
    ssize_t len = readlink("/proc/self/exe", path, sizeof(path) - 1);
    if (len != -1) {
        path[len] = '\0';
        std::string exe_path(path);
        return exe_path.substr(0, exe_path.find_last_of("/"));
    }
    return ".";
#endif
}

bool ConfigManager::ReadFile(const std::string& file_path, std::string& content) {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        return false;
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    content = buffer.str();
    return true;
}

bool ConfigManager::WriteFile(const std::string& file_path, const std::string& content) {
    std::ofstream file(file_path);
    if (!file.is_open()) {
        return false;
    }

    file << content;
    return file.good();
}

bool ConfigManager::CreateDirectoryIfNotExists(const std::string& dir_path) {
    try {
        std::filesystem::create_directories(dir_path);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to create directory: " << e.what() << std::endl;
        return false;
    }
}

bool ConfigManager::LoadFromJSON(const std::string& json_content) {
    try {
        // Parse keyboard settings
        config_.keyboard.auto_layout = SimpleJSON::GetBoolValue(json_content, "keyboard_auto_layout");
        config_.keyboard.keyboard_margin = SimpleJSON::GetFloatValue(json_content, "keyboard_margin");
        config_.keyboard.white_key_width = SimpleJSON::GetFloatValue(json_content, "white_key_width");
        config_.keyboard.white_key_height = SimpleJSON::GetFloatValue(json_content, "white_key_height");
        config_.keyboard.black_key_width = SimpleJSON::GetFloatValue(json_content, "black_key_width");
        config_.keyboard.black_key_height = SimpleJSON::GetFloatValue(json_content, "black_key_height");

        // Parse audio settings
        config_.audio.audio_enabled = SimpleJSON::GetBoolValue(json_content, "audio_enabled");
        config_.audio.volume = SimpleJSON::GetFloatValue(json_content, "audio_volume");
        config_.audio.soundfont_path = SimpleJSON::GetStringValue(json_content, "soundfont_path");
        config_.audio.polyphony = SimpleJSON::GetIntValue(json_content, "audio_polyphony");
        config_.audio.limiter_enabled = SimpleJSON::GetBoolValue(json_content, "limiter_enabled");

        // Parse audio limiter settings
        config_.audio.limiter_threshold = SimpleJSON::GetFloatValue(json_content, "limiter_threshold");
        config_.audio.limiter_ratio = SimpleJSON::GetFloatValue(json_content, "limiter_ratio");
        config_.audio.limiter_attack_time = SimpleJSON::GetFloatValue(json_content, "limiter_attack_time");
        config_.audio.limiter_release_time = SimpleJSON::GetFloatValue(json_content, "limiter_release_time");
        config_.audio.limiter_lookahead_time = SimpleJSON::GetFloatValue(json_content, "limiter_lookahead_time");
        config_.audio.limiter_makeup_gain = SimpleJSON::GetFloatValue(json_content, "limiter_makeup_gain");

        // Parse display settings
        config_.display.background_color[0] = SimpleJSON::GetFloatValue(json_content, "bg_color_r");
        config_.display.background_color[1] = SimpleJSON::GetFloatValue(json_content, "bg_color_g");
        config_.display.background_color[2] = SimpleJSON::GetFloatValue(json_content, "bg_color_b");
        config_.display.show_settings = SimpleJSON::GetBoolValue(json_content, "show_settings");
        config_.display.show_debug = SimpleJSON::GetBoolValue(json_content, "show_debug");
        config_.display.show_bassmidi_status = SimpleJSON::GetBoolValue(json_content, "show_bassmidi_status");
        config_.display.show_midi_input = SimpleJSON::GetBoolValue(json_content, "show_midi_input");
        config_.display.show_audio_limiter = SimpleJSON::GetBoolValue(json_content, "show_audio_limiter");

        // Parse MIDI settings
        config_.midi.selected_midi_device = SimpleJSON::GetIntValue(json_content, "selected_midi_device");
        config_.midi.selected_alsa_midi_device = SimpleJSON::GetIntValue(json_content, "selected_alsa_midi_device");
        config_.midi.use_alsa_midi = SimpleJSON::GetBoolValue(json_content, "use_alsa_midi");

        // Parse window settings
        config_.window.width = SimpleJSON::GetIntValue(json_content, "window_width");
        config_.window.height = SimpleJSON::GetIntValue(json_content, "window_height");
        config_.window.maximized = SimpleJSON::GetBoolValue(json_content, "window_maximized");

        config_dirty_ = false;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error parsing config JSON: " << e.what() << std::endl;
        return false;
    }
}

std::string ConfigManager::SaveToJSON() const {
    std::stringstream json;
    json << "{\n";

    // Keyboard settings
    json << "  \"keyboard_auto_layout\": " << (config_.keyboard.auto_layout ? "true" : "false") << ",\n";
    json << "  \"keyboard_margin\": " << config_.keyboard.keyboard_margin << ",\n";
    json << "  \"white_key_width\": " << config_.keyboard.white_key_width << ",\n";
    json << "  \"white_key_height\": " << config_.keyboard.white_key_height << ",\n";
    json << "  \"black_key_width\": " << config_.keyboard.black_key_width << ",\n";
    json << "  \"black_key_height\": " << config_.keyboard.black_key_height << ",\n";

    // Audio settings
    json << "  \"audio_enabled\": " << (config_.audio.audio_enabled ? "true" : "false") << ",\n";
    json << "  \"audio_volume\": " << config_.audio.volume << ",\n";
    json << "  \"soundfont_path\": \"" << SimpleJSON::EscapeString(config_.audio.soundfont_path) << "\",\n";
    json << "  \"audio_polyphony\": " << config_.audio.polyphony << ",\n";
    json << "  \"limiter_enabled\": " << (config_.audio.limiter_enabled ? "true" : "false") << ",\n";

    // Audio limiter settings
    json << "  \"limiter_threshold\": " << config_.audio.limiter_threshold << ",\n";
    json << "  \"limiter_ratio\": " << config_.audio.limiter_ratio << ",\n";
    json << "  \"limiter_attack_time\": " << config_.audio.limiter_attack_time << ",\n";
    json << "  \"limiter_release_time\": " << config_.audio.limiter_release_time << ",\n";
    json << "  \"limiter_lookahead_time\": " << config_.audio.limiter_lookahead_time << ",\n";
    json << "  \"limiter_makeup_gain\": " << config_.audio.limiter_makeup_gain << ",\n";

    // Display settings
    json << "  \"bg_color_r\": " << config_.display.background_color[0] << ",\n";
    json << "  \"bg_color_g\": " << config_.display.background_color[1] << ",\n";
    json << "  \"bg_color_b\": " << config_.display.background_color[2] << ",\n";
    json << "  \"show_settings\": " << (config_.display.show_settings ? "true" : "false") << ",\n";
    json << "  \"show_debug\": " << (config_.display.show_debug ? "true" : "false") << ",\n";
    json << "  \"show_bassmidi_status\": " << (config_.display.show_bassmidi_status ? "true" : "false") << ",\n";
    json << "  \"show_midi_input\": " << (config_.display.show_midi_input ? "true" : "false") << ",\n";
    json << "  \"show_audio_limiter\": " << (config_.display.show_audio_limiter ? "true" : "false") << ",\n";

    // MIDI settings
    json << "  \"selected_midi_device\": " << config_.midi.selected_midi_device << ",\n";
    json << "  \"selected_alsa_midi_device\": " << config_.midi.selected_alsa_midi_device << ",\n";
    json << "  \"use_alsa_midi\": " << (config_.midi.use_alsa_midi ? "true" : "false") << ",\n";

    // Window settings
    json << "  \"window_width\": " << config_.window.width << ",\n";
    json << "  \"window_height\": " << config_.window.height << ",\n";
    json << "  \"window_maximized\": " << (config_.window.maximized ? "true" : "false") << "\n";

    json << "}";
    return json.str();
}
